{"name": "functions", "scripts": {"build": "tsc", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"firebase-admin": "^11.11.1", "firebase-functions": "^4.9.0"}, "devDependencies": {"typescript": "^4.9.0"}, "private": true}